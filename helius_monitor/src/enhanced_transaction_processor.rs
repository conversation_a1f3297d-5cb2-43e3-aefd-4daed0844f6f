use serde_json::Value;
use std::collections::{HashMap, HashSet};
use std::sync::Arc;
use tokio::sync::Mutex;
use crate::{SubscriptionManager, transaction_utils};

/// Enhanced transaction processor that only processes transactions for subscribed mints
pub struct EnhancedTransactionProcessor {
    pub subscription_manager: Arc<Mutex<SubscriptionManager>>,
    pub successful_tx_count: u64,
    pub total_tx_count: u64,
    pub failed_tx_count: u64,
    pub alerted_tokens: HashSet<String>,
}

impl EnhancedTransactionProcessor {
    pub fn new(subscription_manager: Arc<Mutex<SubscriptionManager>>) -> Self {
        Self {
            subscription_manager,
            successful_tx_count: 0,
            total_tx_count: 0,
            failed_tx_count: 0,
            alerted_tokens: HashSet::new(),
        }
    }

    /// Process a logsNotification message with enhanced filtering
    /// Only processes successful transactions for currently subscribed mints
    pub async fn process_logs_notification(
        &mut self,
        json: &Value,
        window_map: &mut HashMap<String, crate::WindowStats>,
    ) -> Result<(), Box<dyn std::error::Error>> {
        self.total_tx_count += 1;

        // Debug: Log every 1000th transaction to show we're receiving data and filtering stats
        if self.total_tx_count % 1000 == 0 {
            let success_rate = if self.total_tx_count > 0 {
                (self.successful_tx_count as f64 / self.total_tx_count as f64) * 100.0
            } else {
                0.0
            };
            println!("🔍 Debug: Processed {} total transactions | {} successful | {} failed | Success rate: {:.1}%",
                     self.total_tx_count, self.successful_tx_count, self.failed_tx_count, success_rate);
        }

        // FILTER 1: Check if transaction was successful (meta.err is null)
        if !transaction_utils::is_transaction_successful(json) {
            self.failed_tx_count += 1;

            // Log failed transaction filtering every 100 failed transactions for verification
            if self.failed_tx_count % 100 == 0 {
                println!("🚫 Filtered {} failed transactions (meta.err != null)", self.failed_tx_count);
            }

            // Early exit for failed transactions - this prevents processing of failed swaps
            return Ok(());
        }

        // FILTER 2: Determine the specific mint for this transaction using subscription ID
        let subscription_id = json.pointer("/params/subscription").and_then(|s| s.as_u64());

        let target_mint = if let Some(sub_id) = subscription_id {
            let manager = self.subscription_manager.lock().await;
            manager.get_mint_for_subscription(sub_id)
        } else {
            None
        };

        // FILTER 3: Only process transactions for mints we're currently subscribed to
        let is_valid_subscription = if let Some(mint) = &target_mint {
            let manager = self.subscription_manager.lock().await;
            manager.is_subscribed(mint)
        } else {
            false
        };

        if !is_valid_subscription {
            // Silent early exit for unsubscribed mints - no logging to reduce spam
            return Ok(());
        }

        // Only process successful transactions for subscribed mints from here
        self.successful_tx_count += 1;

        // Extract transaction signature (only for successful, subscribed transactions)
        let signature = transaction_utils::extract_signature(json);

        // Process the transaction for the specific mint
        if let Some(mint) = target_mint {
            // Remove the per-transaction logging to reduce console spam
            self.process_mint_transaction(json, &mint, &signature, window_map).await?;
        }

        Ok(())
    }

    /// Process a transaction for a specific mint with clean, focused output
    async fn process_mint_transaction(
        &mut self,
        json: &Value,
        mint: &str,
        signature: &Option<String>,
        window_map: &mut HashMap<String, crate::WindowStats>,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Extract logs for analysis
        let empty_logs = vec![];
        // For logsNotification, logs are under /params/result/value/logs
        let logs = json.pointer("/params/result/value/logs")
            .and_then(|l| l.as_array())
            .or_else(|| {
                // Fallback for transactionNotification format (backward compatibility)
                json.pointer("/params/result/value/meta/logMessages").and_then(|l| l.as_array())
            })
            .unwrap_or(&empty_logs);

        // Enhanced buy/sell detection
        let transaction_type = transaction_utils::detect_buy_sell_type(logs);

        // Extract SOL amount with safer defaults for logsNotification
        let sol_amount = transaction_utils::extract_sol_amount(json)
            .unwrap_or(0.01); // Default to 0.01 SOL if can't extract

        // Convert SOL to lamports for consistent tracking with overflow protection
        let amount_lamports = if sol_amount > 1000.0 {
            // If amount seems too large, cap it to prevent overflow
            1_000_000_000u64 // 1 SOL in lamports
        } else {
            (sol_amount * 1_000_000_000.0) as u64
        };

        match transaction_type {
            Some(true) => {
                // Record buy in sliding window
                let stats = window_map.entry(mint.to_string()).or_insert_with(crate::WindowStats::new);
                stats.record_buy(amount_lamports);

                // Enhanced threshold-crossing detection with proper deduplication
                let (buy_count, _, _, _) = stats.totals();

                // Implement threshold alert deduplication logic
                if buy_count > 4 && !self.alerted_tokens.contains(mint) {
                    // First time crossing threshold - trigger alert and add to triggered set
                    let sig_display = if let Some(sig) = signature {
                        crate::transaction_utils::format_signature_with_url(sig)
                    } else {
                        "<none>".to_string()
                    };
                    println!("🚨 Token {} has {} buys in the last 3 s! Signature: {}",
                             mint, buy_count, sig_display);
                    self.alerted_tokens.insert(mint.to_string());
                } else if buy_count <= 4 && self.alerted_tokens.contains(mint) {
                    // Count dropped below threshold - remove from triggered set to allow future alerts
                    self.alerted_tokens.remove(mint);
                }
                // Otherwise: do nothing (no console output for repeated threshold crossings)
            }
            Some(false) => {
                // Record sell in sliding window
                let stats = window_map.entry(mint.to_string()).or_insert_with(crate::WindowStats::new);
                stats.record_sell(amount_lamports);

                // Check if sell activity should reset alert status for this mint
                let (buy_count, _, _, _) = stats.totals();
                if buy_count <= 4 && self.alerted_tokens.contains(mint) {
                    // Buy count dropped below threshold - remove from triggered set
                    self.alerted_tokens.remove(mint);
                }
            }
            None => {
                // No clear buy/sell pattern detected - this is normal for many transactions
            }
        }

       

        Ok(())
    }




}

/// Helper function to validate that we're receiving transactions for the correct mints
pub async fn validate_subscription_effectiveness(
    subscription_manager: &Arc<Mutex<SubscriptionManager>>,
    processed_mints: &HashMap<String, crate::WindowStats>,
) {
    let subscribed_mints = {
        let manager = subscription_manager.lock().await;
        manager.get_active_mints()
    };

    println!("🔍 Subscription Validation:");
    println!("   • Subscribed mints: {}", subscribed_mints.len());
    println!("   • Mints with activity: {}", processed_mints.len());

    for mint in &subscribed_mints {
        if processed_mints.contains_key(mint) {
            println!("   ✅ {} - receiving transactions", mint);
        } else {
            println!("   ⚠️  {} - no transactions received yet", mint);
        }
    }

    // Check for unexpected mints (shouldn't happen with proper filtering)
    for mint in processed_mints.keys() {
        if !subscribed_mints.contains(mint) {
            println!("   ❌ {} - unexpected mint activity (not subscribed)", mint);
        }
    }
}

/// Test function to debug why we're not detecting pump.fun transactions
pub async fn debug_pump_fun_detection() {
    println!("🧪 Testing pump.fun transaction detection patterns...");

    // Test our buy/sell detection with typical pump.fun patterns
    let test_logs = vec![
        serde_json::json!("Program log: Instruction: Buy"),
        serde_json::json!("Program log: Instruction: Sell"),
        serde_json::json!("Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P invoke [1]"),
        serde_json::json!("Program log: buy"),
        serde_json::json!("Program log: sell"),
        serde_json::json!("Program log: swap"),
    ];

    let buy_result = crate::transaction_utils::detect_buy_sell_type(&test_logs);
    println!("   Buy/sell detection result: {:?}", buy_result);

    // Test mint address validation
    let test_mint = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P"; // pump.fun program
    println!("   Test mint validation: {}", crate::transaction_utils::is_valid_mint_address(test_mint));
}
